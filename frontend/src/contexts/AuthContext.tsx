import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import apiService, { UserProfile, LoginRequest, RegisterRequest } from '../services/api';
import mockAuthService from '../services/mockAuth';

interface AuthContextType {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<{ success: boolean; message?: string }>;
  register: (userData: RegisterRequest) => Promise<{ success: boolean; message?: string }>;
  logout: () => void;
  updateProfile: (profileData: Partial<UserProfile>) => Promise<{ success: boolean; message?: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [useMockAuth, setUseMockAuth] = useState(false);

  const isAuthenticated = !!user;

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('authToken');
        const storedUser = localStorage.getItem('user');

        if (token && storedUser) {
          // Try real API first
          try {
            const response = await apiService.getUserProfile();
            if (response.success && response.data) {
              setUser(response.data);
              setUseMockAuth(false);
              return;
            }
          } catch (apiError) {
            console.log('Real API not available, using mock auth');
            setUseMockAuth(true);
          }

          // Fall back to mock auth
          const mockResponse = await mockAuthService.getUserProfile();
          if (mockResponse.success && mockResponse.data) {
            setUser(mockResponse.data);
            setUseMockAuth(true);
          } else {
            // Clear invalid auth data
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Clear invalid tokens
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        setUseMockAuth(true);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginRequest): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);

      // Try real API first
      try {
        const response = await apiService.login(credentials);
        if (response.success && response.data) {
          const { user: userData, token, refreshToken } = response.data;

          // Store auth data
          localStorage.setItem('authToken', token);
          if (refreshToken) {
            localStorage.setItem('refreshToken', refreshToken);
          }
          localStorage.setItem('user', JSON.stringify(userData));

          setUser(userData);
          setUseMockAuth(false);
          return { success: true, message: response.message };
        } else {
          return { success: false, message: response.error || 'Login failed' };
        }
      } catch (apiError) {
        console.log('Real API not available, trying mock auth');
        setUseMockAuth(true);

        // Fall back to mock auth
        const mockResponse = await mockAuthService.login(credentials.email, credentials.password);
        if (mockResponse.success && mockResponse.data) {
          setUser(mockResponse.data.user);
          return { success: true, message: 'Login successful (mock mode)' };
        } else {
          return { success: false, message: mockResponse.message || 'Login failed' };
        }
      }
    } catch (error: any) {
      console.error('Login error:', error);
      const message = error.response?.data?.message || error.message || 'Login failed';
      return { success: false, message };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterRequest): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);
      const response = await apiService.register(userData);

      if (response.success && response.data) {
        const { user: newUser, token, refreshToken } = response.data;
        
        // Store auth data
        localStorage.setItem('authToken', token);
        if (refreshToken) {
          localStorage.setItem('refreshToken', refreshToken);
        }
        localStorage.setItem('user', JSON.stringify(newUser));
        
        setUser(newUser);
        return { success: true, message: response.message };
      } else {
        return { success: false, message: response.error || 'Registration failed' };
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      const message = error.response?.data?.message || error.message || 'Registration failed';
      return { success: false, message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Clear auth data
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    setUser(null);

    // Call logout endpoint (fire and forget)
    if (useMockAuth) {
      mockAuthService.logout().catch(console.error);
    } else {
      apiService.logout().catch(console.error);
    }
  };

  const updateProfile = async (profileData: Partial<UserProfile>): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);
      const response = await apiService.updateUserProfile(profileData);

      if (response.success && response.data) {
        const updatedUser = response.data;
        localStorage.setItem('user', JSON.stringify(updatedUser));
        setUser(updatedUser);
        return { success: true, message: response.message };
      } else {
        return { success: false, message: response.error || 'Profile update failed' };
      }
    } catch (error: any) {
      console.error('Profile update error:', error);
      const message = error.response?.data?.message || error.message || 'Profile update failed';
      return { success: false, message };
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
