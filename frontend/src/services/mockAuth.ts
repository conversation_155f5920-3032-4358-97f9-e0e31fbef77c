// Mock Authentication Service for Testing Dashboard
// This bypasses the real backend for quick testing

export interface MockUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: 'CANDIDATE' | 'RECRUITER' | 'ADMIN';
  isVerified: boolean;
  avatar?: string;
  phone?: string;
  location?: string;
  createdAt: string;
}

export interface MockAuthResponse {
  user: MockUser;
  token: string;
  refreshToken?: string;
}

// Mock users for testing
const mockUsers: MockUser[] = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    userType: 'CANDIDATE',
    isVerified: true,
    avatar: 'https://via.placeholder.com/150x150/2563eb/ffffff?text=JD',
    phone: '+234-************',
    location: 'Lagos, Nigeria',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    userType: 'RECRUITER',
    isVerified: true,
    avatar: 'https://via.placeholder.com/150x150/059669/ffffff?text=SJ',
    phone: '+27-21-123-4567',
    location: 'Cape Town, South Africa',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    userType: 'ADMIN',
    isVerified: true,
    avatar: 'https://via.placeholder.com/150x150/dc2626/ffffff?text=AU',
    phone: '******-123-4567',
    location: 'Global',
    createdAt: '2024-01-01T00:00:00Z'
  }
];

// Mock credentials
const mockCredentials = [
  { email: '<EMAIL>', password: 'Candidate123!' },
  { email: '<EMAIL>', password: 'Recruiter123!' },
  { email: '<EMAIL>', password: 'Admin123!' },
  // Easy test credentials
  { email: '<EMAIL>', password: 'test123' },
  { email: '<EMAIL>', password: 'test123' },
  { email: '<EMAIL>', password: 'test123' }
];

class MockAuthService {
  private delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async login(email: string, password: string): Promise<{ success: boolean; data?: MockAuthResponse; message?: string }> {
    await this.delay();

    // Check credentials
    const credential = mockCredentials.find(c => c.email === email && c.password === password);
    if (!credential) {
      return {
        success: false,
        message: 'Invalid email or password'
      };
    }

    // Find user
    let user = mockUsers.find(u => u.email === email);
    
    // If not found in predefined users, create a mock user based on email
    if (!user) {
      const userType = email.includes('recruiter') ? 'RECRUITER' : 
                      email.includes('admin') ? 'ADMIN' : 'CANDIDATE';
      
      user = {
        id: Date.now().toString(),
        email,
        firstName: email.includes('recruiter') ? 'Test' : 
                  email.includes('admin') ? 'Admin' : 'Test',
        lastName: email.includes('recruiter') ? 'Recruiter' : 
                 email.includes('admin') ? 'User' : 'Candidate',
        userType,
        isVerified: true,
        avatar: `https://via.placeholder.com/150x150/2563eb/ffffff?text=${userType.charAt(0)}`,
        createdAt: new Date().toISOString()
      };
    }

    const token = `mock-token-${user.id}-${Date.now()}`;
    const refreshToken = `mock-refresh-${user.id}-${Date.now()}`;

    // Store in localStorage for persistence
    localStorage.setItem('authToken', token);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('user', JSON.stringify(user));

    return {
      success: true,
      data: {
        user,
        token,
        refreshToken
      }
    };
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    userType: 'CANDIDATE' | 'RECRUITER';
  }): Promise<{ success: boolean; data?: MockAuthResponse; message?: string }> {
    await this.delay();

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === userData.email);
    if (existingUser) {
      return {
        success: false,
        message: 'User with this email already exists'
      };
    }

    // Create new user
    const newUser: MockUser = {
      id: Date.now().toString(),
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      userType: userData.userType,
      isVerified: true,
      avatar: `https://via.placeholder.com/150x150/2563eb/ffffff?text=${userData.firstName.charAt(0)}${userData.lastName.charAt(0)}`,
      createdAt: new Date().toISOString()
    };

    const token = `mock-token-${newUser.id}-${Date.now()}`;
    const refreshToken = `mock-refresh-${newUser.id}-${Date.now()}`;

    // Store in localStorage
    localStorage.setItem('authToken', token);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('user', JSON.stringify(newUser));

    return {
      success: true,
      data: {
        user: newUser,
        token,
        refreshToken
      }
    };
  }

  async getUserProfile(): Promise<{ success: boolean; data?: MockUser; message?: string }> {
    await this.delay(200);

    const token = localStorage.getItem('authToken');
    const userStr = localStorage.getItem('user');

    if (!token || !userStr) {
      return {
        success: false,
        message: 'Not authenticated'
      };
    }

    try {
      const user = JSON.parse(userStr);
      return {
        success: true,
        data: user
      };
    } catch (error) {
      return {
        success: false,
        message: 'Invalid user data'
      };
    }
  }

  async logout(): Promise<{ success: boolean; message?: string }> {
    await this.delay(200);

    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');

    return {
      success: true,
      message: 'Logged out successfully'
    };
  }

  async updateProfile(profileData: Partial<MockUser>): Promise<{ success: boolean; data?: MockUser; message?: string }> {
    await this.delay();

    const userStr = localStorage.getItem('user');
    if (!userStr) {
      return {
        success: false,
        message: 'Not authenticated'
      };
    }

    try {
      const user = JSON.parse(userStr);
      const updatedUser = { ...user, ...profileData };
      localStorage.setItem('user', JSON.stringify(updatedUser));

      return {
        success: true,
        data: updatedUser
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to update profile'
      };
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    return !!(token && user);
  }

  // Get current user from localStorage
  getCurrentUser(): MockUser | null {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    try {
      return JSON.parse(userStr);
    } catch (error) {
      return null;
    }
  }
}

export const mockAuthService = new MockAuthService();
export default mockAuthService;
