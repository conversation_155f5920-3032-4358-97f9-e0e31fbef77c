import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  userType: 'CANDIDATE' | 'RECRUITER';
}

export interface AuthResponse {
  user: UserProfile;
  token: string;
  refreshToken?: string;
}

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: 'CANDIDATE' | 'RECRUITER' | 'ADMIN';
  isVerified: boolean;
  avatar?: string;
  phone?: string;
  location?: string;
  createdAt: string;
}

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3002',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor to handle errors
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error: AxiosError) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

const api = createApiInstance();

// API Service Class
class ApiService {
  // Health check
  async healthCheck(): Promise<ApiResponse> {
    const response = await api.get('/health');
    return response.data;
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await api.post('/api/auth/login', credentials);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await api.post('/api/auth/register', userData);
    return response.data;
  }

  async logout(): Promise<ApiResponse> {
    const response = await api.post('/api/auth/logout');
    return response.data;
  }

  // User endpoints
  async getUserProfile(): Promise<ApiResponse<UserProfile>> {
    const response = await api.get('/api/users/profile');
    return response.data;
  }

  async updateUserProfile(profileData: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    const response = await api.put('/api/users/profile', profileData);
    return response.data;
  }

  async getUserById(userId: string): Promise<ApiResponse<UserProfile>> {
    const response = await api.get(`/api/users/${userId}`);
    return response.data;
  }

  // Jobs endpoints
  async getJobs(params?: {
    keywords?: string;
    location?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await api.get('/api/jobs', { params });
    return response.data;
  }

  async getJobById(jobId: string): Promise<ApiResponse<any>> {
    const response = await api.get(`/api/jobs/${jobId}`);
    return response.data;
  }

  // Companies endpoints
  async getCompanies(params?: {
    page?: number;
    limit?: number;
    search?: string;
    industry?: string;
    size?: string;
    location?: string;
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await api.get('/api/companies', { params });
    return response.data;
  }

  async getCompanyById(companyId: string): Promise<ApiResponse<any>> {
    const response = await api.get(`/api/companies/${companyId}`);
    return response.data;
  }

  async createCompany(companyData: {
    name: string;
    description: string;
    website?: string;
    logo?: string;
    industry: string;
    size?: string;
    location?: string;
    foundedYear?: number;
  }): Promise<ApiResponse<any>> {
    const response = await api.post('/api/companies', companyData);
    return response.data;
  }

  // Applications endpoints
  async getApplications(params?: {
    page?: number;
    limit?: number;
    status?: string;
    jobId?: string;
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await api.get('/api/applications', { params });
    return response.data;
  }

  async submitApplication(applicationData: {
    jobId: string;
    coverLetter?: string;
    resumeUrl?: string;
  }): Promise<ApiResponse<any>> {
    const response = await api.post('/api/applications', applicationData);
    return response.data;
  }

  async updateApplicationStatus(applicationId: string, status: string): Promise<ApiResponse<any>> {
    const response = await api.put(`/api/applications/${applicationId}/status`, { status });
    return response.data;
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;

// Export individual methods for convenience
export const {
  healthCheck,
  login,
  register,
  logout,
  getUserProfile,
  updateUserProfile,
  getUserById,
  getJobs,
  getJobById,
  getCompanies,
  getCompanyById,
  createCompany,
  getApplications,
  submitApplication,
  updateApplicationStatus,
} = apiService;
