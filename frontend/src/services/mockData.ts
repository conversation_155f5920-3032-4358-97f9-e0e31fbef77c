// Mock Data Service for BytePath Dashboard
// This provides realistic data for development and testing

export interface MockJob {
  id: string;
  title: string;
  company: string;
  location: string;
  type: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'FREELANCE' | 'INTERNSHIP';
  experienceLevel: 'ENTRY_LEVEL' | 'JUNIOR' | 'MID_LEVEL' | 'SENIOR' | 'LEAD' | 'EXECUTIVE';
  salary: {
    min: number;
    max: number;
    currency: string;
  };
  skills: string[];
  description: string;
  requirements: string[];
  postedDate: string;
  deadline: string;
  isRemote: boolean;
  companyLogo?: string;
}

export interface MockApplication {
  id: string;
  jobId: string;
  jobTitle: string;
  company: string;
  status: 'PENDING' | 'REVIEWED' | 'INTERVIEW_SCHEDULED' | 'INTERVIEWED' | 'OFFERED' | 'ACCEPTED' | 'REJECTED' | 'WITHDRAWN';
  appliedDate: string;
  lastUpdate: string;
  notes?: string;
  interviewDate?: string;
}

export interface MockDocument {
  id: string;
  name: string;
  type: 'RESUME' | 'COVER_LETTER' | 'PORTFOLIO' | 'CERTIFICATE' | 'OTHER';
  size: number;
  uploadDate: string;
  url: string;
  isPublic: boolean;
}

export interface MockProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  title?: string;
  bio?: string;
  skills: string[];
  experience: {
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    description: string;
  }[];
  education: {
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
  }[];
  profileCompletion: number;
  avatar?: string;
}

export interface MockStats {
  applications: {
    total: number;
    pending: number;
    interviews: number;
    offers: number;
  };
  profile: {
    views: number;
    completeness: number;
    lastUpdated: string;
  };
  jobs: {
    saved: number;
    applied: number;
    matched: number;
  };
}

// Mock Data
export const mockJobs: MockJob[] = [
  {
    id: '1',
    title: 'Senior Frontend Developer',
    company: 'TechCorp Africa',
    location: 'Lagos, Nigeria',
    type: 'FULL_TIME',
    experienceLevel: 'SENIOR',
    salary: { min: 80000, max: 120000, currency: 'USD' },
    skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
    description: 'We are looking for a senior frontend developer to join our growing team...',
    requirements: ['5+ years React experience', 'TypeScript proficiency', 'Team leadership skills'],
    postedDate: '2024-01-15',
    deadline: '2024-02-15',
    isRemote: true,
    companyLogo: 'https://via.placeholder.com/100x100/2563eb/ffffff?text=TC'
  },
  {
    id: '2',
    title: 'Full Stack Engineer',
    company: 'StartupHub Kenya',
    location: 'Nairobi, Kenya',
    type: 'FULL_TIME',
    experienceLevel: 'MID_LEVEL',
    salary: { min: 60000, max: 90000, currency: 'USD' },
    skills: ['Python', 'Django', 'React', 'PostgreSQL'],
    description: 'Join our innovative fintech startup as a full stack engineer...',
    requirements: ['3+ years full stack experience', 'Python/Django expertise', 'Fintech experience preferred'],
    postedDate: '2024-01-20',
    deadline: '2024-02-20',
    isRemote: false,
    companyLogo: 'https://via.placeholder.com/100x100/059669/ffffff?text=SH'
  },
  {
    id: '3',
    title: 'DevOps Engineer',
    company: 'CloudTech Solutions',
    location: 'Cape Town, South Africa',
    type: 'CONTRACT',
    experienceLevel: 'SENIOR',
    salary: { min: 70000, max: 100000, currency: 'USD' },
    skills: ['AWS', 'Docker', 'Kubernetes', 'Terraform'],
    description: 'We need an experienced DevOps engineer for a 6-month contract...',
    requirements: ['AWS certification', 'Kubernetes experience', 'CI/CD pipeline expertise'],
    postedDate: '2024-01-25',
    deadline: '2024-02-25',
    isRemote: true,
    companyLogo: 'https://via.placeholder.com/100x100/dc2626/ffffff?text=CT'
  }
];

export const mockApplications: MockApplication[] = [
  {
    id: '1',
    jobId: '1',
    jobTitle: 'Senior Frontend Developer',
    company: 'TechCorp Africa',
    status: 'INTERVIEW_SCHEDULED',
    appliedDate: '2024-01-16',
    lastUpdate: '2024-01-22',
    notes: 'Technical interview scheduled for next week',
    interviewDate: '2024-01-30'
  },
  {
    id: '2',
    jobId: '2',
    jobTitle: 'Full Stack Engineer',
    company: 'StartupHub Kenya',
    status: 'REVIEWED',
    appliedDate: '2024-01-21',
    lastUpdate: '2024-01-24',
    notes: 'Application under review by hiring manager'
  },
  {
    id: '3',
    jobId: '3',
    jobTitle: 'DevOps Engineer',
    company: 'CloudTech Solutions',
    status: 'PENDING',
    appliedDate: '2024-01-26',
    lastUpdate: '2024-01-26',
    notes: 'Application submitted successfully'
  }
];

export const mockDocuments: MockDocument[] = [
  {
    id: '1',
    name: 'John_Doe_Resume_2024.pdf',
    type: 'RESUME',
    size: 245760,
    uploadDate: '2024-01-15',
    url: '/documents/resume.pdf',
    isPublic: true
  },
  {
    id: '2',
    name: 'Cover_Letter_TechCorp.pdf',
    type: 'COVER_LETTER',
    size: 128000,
    uploadDate: '2024-01-16',
    url: '/documents/cover-letter.pdf',
    isPublic: false
  },
  {
    id: '3',
    name: 'Portfolio_Website.pdf',
    type: 'PORTFOLIO',
    size: 512000,
    uploadDate: '2024-01-10',
    url: '/documents/portfolio.pdf',
    isPublic: true
  },
  {
    id: '4',
    name: 'AWS_Certification.pdf',
    type: 'CERTIFICATE',
    size: 180000,
    uploadDate: '2024-01-05',
    url: '/documents/aws-cert.pdf',
    isPublic: true
  }
];

export const mockProfile: MockProfile = {
  id: '1',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+234-************',
  location: 'Lagos, Nigeria',
  title: 'Senior Full Stack Developer',
  bio: 'Passionate full stack developer with 6+ years of experience building scalable web applications. Specialized in React, Node.js, and cloud technologies.',
  skills: ['React', 'TypeScript', 'Node.js', 'Python', 'AWS', 'Docker', 'GraphQL', 'PostgreSQL'],
  experience: [
    {
      company: 'TechStart Nigeria',
      position: 'Senior Full Stack Developer',
      startDate: '2022-03',
      description: 'Led development of fintech platform serving 100k+ users. Built microservices architecture using Node.js and React.'
    },
    {
      company: 'Digital Solutions Ltd',
      position: 'Full Stack Developer',
      startDate: '2020-01',
      endDate: '2022-02',
      description: 'Developed e-commerce platforms and mobile applications. Worked with React Native and Django.'
    }
  ],
  education: [
    {
      institution: 'University of Lagos',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      startDate: '2016-09',
      endDate: '2020-06'
    }
  ],
  profileCompletion: 85,
  avatar: 'https://via.placeholder.com/150x150/2563eb/ffffff?text=JD'
};

export const mockStats: MockStats = {
  applications: {
    total: 12,
    pending: 4,
    interviews: 3,
    offers: 1
  },
  profile: {
    views: 47,
    completeness: 85,
    lastUpdated: '2024-01-25'
  },
  jobs: {
    saved: 8,
    applied: 12,
    matched: 23
  }
};

// Mock Data Service Class
class MockDataService {
  // Simulate API delay
  private delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getJobs(): Promise<MockJob[]> {
    await this.delay();
    return mockJobs;
  }

  async getJobById(id: string): Promise<MockJob | null> {
    await this.delay();
    return mockJobs.find(job => job.id === id) || null;
  }

  async getApplications(): Promise<MockApplication[]> {
    await this.delay();
    return mockApplications;
  }

  async getDocuments(): Promise<MockDocument[]> {
    await this.delay();
    return mockDocuments;
  }

  async getProfile(): Promise<MockProfile> {
    await this.delay();
    return mockProfile;
  }

  async getStats(): Promise<MockStats> {
    await this.delay();
    return mockStats;
  }

  async updateProfile(updates: Partial<MockProfile>): Promise<MockProfile> {
    await this.delay();
    return { ...mockProfile, ...updates };
  }

  async uploadDocument(file: File, type: MockDocument['type']): Promise<MockDocument> {
    await this.delay();
    const newDoc: MockDocument = {
      id: Date.now().toString(),
      name: file.name,
      type,
      size: file.size,
      uploadDate: new Date().toISOString().split('T')[0],
      url: `/documents/${file.name}`,
      isPublic: false
    };
    return newDoc;
  }

  // Recruiter-specific data
  async getRecruiterStats(): Promise<{
    jobsPosted: number;
    activeJobs: number;
    totalApplications: number;
    candidatesInterviewed: number;
    hires: number;
  }> {
    await this.delay();
    return {
      jobsPosted: 8,
      activeJobs: 5,
      totalApplications: 156,
      candidatesInterviewed: 23,
      hires: 4
    };
  }

  async getCandidates(): Promise<Array<{
    id: string;
    name: string;
    title: string;
    location: string;
    skills: string[];
    experience: string;
    avatar?: string;
    appliedJobs: number;
    status: 'NEW' | 'REVIEWED' | 'SHORTLISTED' | 'INTERVIEWED' | 'HIRED' | 'REJECTED';
  }>> {
    await this.delay();
    return [
      {
        id: '1',
        name: 'Sarah Johnson',
        title: 'Senior React Developer',
        location: 'Lagos, Nigeria',
        skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
        experience: '5+ years',
        appliedJobs: 3,
        status: 'SHORTLISTED',
        avatar: 'https://via.placeholder.com/60x60/2563eb/ffffff?text=SJ'
      },
      {
        id: '2',
        name: 'Michael Chen',
        title: 'Full Stack Engineer',
        location: 'Nairobi, Kenya',
        skills: ['Python', 'Django', 'React', 'PostgreSQL'],
        experience: '4+ years',
        appliedJobs: 2,
        status: 'INTERVIEWED',
        avatar: 'https://via.placeholder.com/60x60/059669/ffffff?text=MC'
      },
      {
        id: '3',
        name: 'Amara Okafor',
        title: 'DevOps Engineer',
        location: 'Cape Town, South Africa',
        skills: ['AWS', 'Docker', 'Kubernetes', 'Terraform'],
        experience: '6+ years',
        appliedJobs: 1,
        status: 'NEW',
        avatar: 'https://via.placeholder.com/60x60/dc2626/ffffff?text=AO'
      }
    ];
  }
}

export const mockDataService = new MockDataService();
export default mockDataService;
