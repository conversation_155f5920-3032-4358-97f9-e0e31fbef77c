/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom styles */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* BytePath custom styles with improved contrast */

/* Primary Colors - Deep Blue Theme */
:root {
  --bytepath-primary: #1e40af;
  --bytepath-primary-dark: #1e3a8a;
  --bytepath-primary-light: #3b82f6;
  --bytepath-secondary: #059669;
  --bytepath-secondary-dark: #047857;
  --bytepath-accent: #dc2626;
  --bytepath-dark: #1f2937;
  --bytepath-light: #f8fafc;
  --bytepath-gray: #6b7280;
}

/* Hero Section with Strong Contrast */
.hero-gradient {
  background: linear-gradient(135deg, var(--bytepath-primary) 0%, var(--bytepath-primary-dark) 100%);
  color: white;
  position: relative;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.hero-gradient > * {
  position: relative;
  z-index: 2;
}

/* Button Styles with Better Contrast */
.btn-primary-custom {
  background-color: var(--bytepath-secondary);
  border-color: var(--bytepath-secondary);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary-custom:hover {
  background-color: var(--bytepath-secondary-dark);
  border-color: var(--bytepath-secondary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  color: white;
}

.btn-secondary-custom {
  background-color: transparent;
  border: 2px solid white;
  color: white;
  font-weight: 600;
  padding: 10px 22px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-secondary-custom:hover {
  background-color: white;
  color: var(--bytepath-primary);
  transform: translateY(-1px);
}

/* Text Colors with High Contrast */
.text-primary-600 {
  color: var(--bytepath-primary) !important;
}

.text-primary-400 {
  color: var(--bytepath-primary-light) !important;
}

.text-secondary-600 {
  color: var(--bytepath-secondary) !important;
}

/* Background Colors */
.bg-gray-50 {
  background-color: var(--bytepath-light);
}

.bg-gray-100 {
  background-color: #f1f5f9;
}

.bg-gray-900 {
  background-color: var(--bytepath-dark);
}

/* Utility Classes */
.min-h-screen {
  min-height: 100vh;
}

.min-vh-75 {
  min-height: 75vh;
}

/* Card Enhancements */
.card {
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Footer Improvements */
.footer-dark {
  background: linear-gradient(135deg, var(--bytepath-dark) 0%, #111827 100%);
  color: #e5e7eb;
}

.footer-dark h5, .footer-dark h6 {
  color: white;
}

.footer-dark a {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.footer-dark a:hover {
  color: var(--bytepath-primary-light);
  text-decoration: none;
}

/* High Contrast Text Improvements */
.text-muted {
  color: #4b5563 !important;
}

.lead {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 400;
}

/* Feature Cards */
.feature-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  color: var(--bytepath-primary);
  margin-bottom: 1rem;
}

/* Statistics Section */
.stats-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Improved Button Focus States */
.btn:focus,
.btn-primary-custom:focus,
.btn-secondary-custom:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  outline: none;
}

/* Navbar Improvements */
.navbar-brand:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* Modern Dashboard Styles */
.dashboard-container {
  background: var(--bytepath-light);
  min-height: 100vh;
  padding: 2rem 0;
}

.dashboard-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.dashboard-welcome {
  font-size: 2rem;
  font-weight: 700;
  color: var(--bytepath-dark);
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: var(--bytepath-gray);
  font-size: 1.1rem;
  margin-bottom: 0;
}

/* Dashboard Grid System */
.dashboard-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  margin-bottom: 2rem;
}

.dashboard-stats-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  margin-bottom: 2rem;
}

/* Modern Stat Cards */
.modern-stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  text-align: center;
}

.modern-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
}

.stat-icon.primary {
  background: rgba(30, 64, 175, 0.1);
  color: var(--bytepath-primary);
}

.stat-icon.success {
  background: rgba(5, 150, 105, 0.1);
  color: var(--bytepath-secondary);
}

.stat-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.stat-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.modern-stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--bytepath-dark);
}

.modern-stat-label {
  color: var(--bytepath-gray);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Dashboard Cards */
.dashboard-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  overflow: hidden;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dashboard-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.dashboard-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--bytepath-dark);
  margin-bottom: 0;
}

.dashboard-card-body {
  padding: 1.5rem;
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
}

.action-btn-primary {
  background: var(--bytepath-primary);
  color: white;
}

.action-btn-primary:hover {
  background: var(--bytepath-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(30, 64, 175, 0.3);
  color: white;
  text-decoration: none;
}

.action-btn-secondary {
  background: #f1f5f9;
  color: var(--bytepath-gray);
  border: 1px solid #e2e8f0;
}

.action-btn-secondary:hover {
  background: #e2e8f0;
  color: var(--bytepath-dark);
  text-decoration: none;
}

/* Progress Indicators */
.progress-modern {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  overflow: hidden;
}

.progress-bar-modern {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-bar-primary {
  background: linear-gradient(90deg, var(--bytepath-primary), var(--bytepath-primary-light));
}

.progress-bar-success {
  background: linear-gradient(90deg, var(--bytepath-secondary), #10b981);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-header {
    padding: 1.5rem;
  }

  .dashboard-welcome {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-container {
    padding: 1rem 0;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
