/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom styles */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* BytePath custom styles with improved contrast */

/* Primary Colors - Deep Blue Theme */
:root {
  --bytepath-primary: #1e40af;
  --bytepath-primary-dark: #1e3a8a;
  --bytepath-primary-light: #3b82f6;
  --bytepath-secondary: #059669;
  --bytepath-secondary-dark: #047857;
  --bytepath-accent: #dc2626;
  --bytepath-dark: #1f2937;
  --bytepath-light: #f8fafc;
  --bytepath-gray: #6b7280;
}

/* Hero Section with Strong Contrast */
.hero-gradient {
  background: linear-gradient(135deg, var(--bytepath-primary) 0%, var(--bytepath-primary-dark) 100%);
  color: white;
  position: relative;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.hero-gradient > * {
  position: relative;
  z-index: 2;
}

/* Button Styles with Better Contrast */
.btn-primary-custom {
  background-color: var(--bytepath-secondary);
  border-color: var(--bytepath-secondary);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary-custom:hover {
  background-color: var(--bytepath-secondary-dark);
  border-color: var(--bytepath-secondary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  color: white;
}

.btn-secondary-custom {
  background-color: transparent;
  border: 2px solid white;
  color: white;
  font-weight: 600;
  padding: 10px 22px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-secondary-custom:hover {
  background-color: white;
  color: var(--bytepath-primary);
  transform: translateY(-1px);
}

/* Text Colors with High Contrast */
.text-primary-600 {
  color: var(--bytepath-primary) !important;
}

.text-primary-400 {
  color: var(--bytepath-primary-light) !important;
}

.text-secondary-600 {
  color: var(--bytepath-secondary) !important;
}

/* Background Colors */
.bg-gray-50 {
  background-color: var(--bytepath-light);
}

.bg-gray-100 {
  background-color: #f1f5f9;
}

.bg-gray-900 {
  background-color: var(--bytepath-dark);
}

/* Utility Classes */
.min-h-screen {
  min-height: 100vh;
}

.min-vh-75 {
  min-height: 75vh;
}

/* Card Enhancements */
.card {
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Footer Improvements */
.footer-dark {
  background: linear-gradient(135deg, var(--bytepath-dark) 0%, #111827 100%);
  color: #e5e7eb;
}

.footer-dark h5, .footer-dark h6 {
  color: white;
}

.footer-dark a {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.footer-dark a:hover {
  color: var(--bytepath-primary-light);
  text-decoration: none;
}

/* High Contrast Text Improvements */
.text-muted {
  color: #4b5563 !important;
}

.lead {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 400;
}

/* Feature Cards */
.feature-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  color: var(--bytepath-primary);
  margin-bottom: 1rem;
}

/* Statistics Section */
.stats-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stats-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Improved Button Focus States */
.btn:focus,
.btn-primary-custom:focus,
.btn-secondary-custom:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  outline: none;
}

/* Navbar Improvements */
.navbar-brand:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
