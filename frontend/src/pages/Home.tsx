import React from 'react';
import { Container, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

const Home: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div>
      {/* Hero Section */}
      <section className="hero-gradient py-5">
        <Container>
          <Row className="align-items-center min-vh-75">
            <Col lg={6}>
              <h1 className="display-4 fw-bold mb-4">
                Connect with Africa's Top Tech Talent
              </h1>
              <p className="lead mb-4">
                BytePath bridges the gap between exceptional African developers, designers, 
                and tech professionals with innovative companies worldwide.
              </p>
              <div className="d-flex gap-3 flex-wrap">
                <Button
                  variant="light"
                  size="lg"
                  className="btn-primary-custom"
                  onClick={() => navigate('/jobs')}
                >
                  Find Your Dream Job
                </Button>
                <Button
                  size="lg"
                  className="btn-secondary-custom"
                  onClick={() => navigate('/register')}
                >
                  Post a Job
                </Button>
              </div>
            </Col>
            <Col lg={6} className="text-center">
              <div className="stats-card">
                <h3 className="mb-4 text-white">🚀 Join the Movement</h3>
                <div className="row text-center">
                  <div className="col-4">
                    <div className="stats-number">1000+</div>
                    <div className="stats-label">Tech Professionals</div>
                  </div>
                  <div className="col-4">
                    <div className="stats-number">500+</div>
                    <div className="stats-label">Companies</div>
                  </div>
                  <div className="col-4">
                    <div className="stats-number">2000+</div>
                    <div className="stats-label">Jobs Posted</div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Features Section */}
      <section className="py-5">
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2 className="fw-bold mb-3">Why Choose BytePath?</h2>
              <p className="text-muted">
                We're more than just a job board - we're your career partner
              </p>
            </Col>
          </Row>
          
          <Row>
            <Col md={4} className="mb-4">
              <div className="feature-card h-100 text-center">
                <div className="feature-icon">
                  <i className="fas fa-users fa-3x"></i>
                </div>
                <h5 className="fw-bold mb-3 text-dark">Curated Talent Pool</h5>
                <p className="text-muted">
                  Access to pre-vetted, skilled professionals across various tech disciplines
                </p>
              </div>
            </Col>
            
            <Col md={4} className="mb-4">
              <div className="feature-card h-100 text-center">
                <div className="feature-icon">
                  <i className="fas fa-rocket fa-3x"></i>
                </div>
                <h5 className="fw-bold mb-3 text-dark">Fast Matching</h5>
                <p className="text-muted">
                  AI-powered matching algorithm connects the right talent with the right opportunities
                </p>
              </div>
            </Col>
            
            <Col md={4} className="mb-4">
              <div className="feature-card h-100 text-center">
                <div className="feature-icon">
                  <i className="fas fa-globe-africa fa-3x"></i>
                </div>
                <h5 className="fw-bold mb-3 text-dark">Africa-Focused</h5>
                <p className="text-muted">
                  Dedicated to showcasing and empowering Africa's incredible tech ecosystem
                </p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-100 py-5">
        <Container>
          <Row className="text-center">
            <Col>
              <h2 className="fw-bold mb-3">Ready to Get Started?</h2>
              <p className="text-muted mb-4">
                Join thousands of professionals and companies already using BytePath
              </p>
              <div className="d-flex justify-content-center gap-3 flex-wrap">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => navigate('/register')}
                >
                  Create Your Profile
                </Button>
                <Button
                  variant="outline-primary"
                  size="lg"
                  onClick={() => navigate('/jobs')}
                >
                  Browse Jobs
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default Home;
