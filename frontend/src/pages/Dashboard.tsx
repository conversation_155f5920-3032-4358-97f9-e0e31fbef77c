import React, { useEffect } from 'react';
import { Container, Spinner } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import CandidateDashboard from '../components/dashboard/CandidateDashboard';
import RecruiterDashboard from '../components/dashboard/RecruiterDashboard';

const Dashboard: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  if (isLoading) {
    return (
      <div className="dashboard-container">
        <Container className="text-center py-5">
          <Spinner animation="border" role="status" variant="primary">
            <span className="visually-hidden">Loading dashboard...</span>
          </Spinner>
          <p className="mt-3 text-muted">Loading your dashboard...</p>
        </Container>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  // Render role-specific dashboard
  switch (user.userType) {
    case 'CANDIDATE':
      return <CandidateDashboard />;
    case 'RECRUITER':
      return <RecruiterDashboard />;
    case 'ADMIN':
      // For now, show candidate dashboard for admin users
      // TODO: Create AdminDashboard component
      return <CandidateDashboard />;
    default:
      return <CandidateDashboard />;
  }


};

export default Dashboard;
