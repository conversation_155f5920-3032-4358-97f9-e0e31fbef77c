import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Collapse } from 'react-bootstrap';

const LoginInfo: React.FC = () => {
  const [showCredentials, setShowCredentials] = useState(false);

  const credentials = [
    {
      type: 'Candidate',
      email: '<EMAIL>',
      password: 'test123',
      description: 'View candidate dashboard with job applications and profile management',
      color: 'primary'
    },
    {
      type: 'Recruiter',
      email: '<EMAIL>',
      password: 'test123',
      description: 'View recruiter dashboard with job postings and candidate pipeline',
      color: 'success'
    },
    {
      type: 'Admin',
      email: '<EMAIL>',
      password: 'test123',
      description: 'View admin dashboard with platform-wide monitoring',
      color: 'warning'
    }
  ];

  const handleQuickLogin = (email: string, password: string) => {
    // Fill the form fields
    const emailInput = document.querySelector('input[name="email"]') as HTMLInputElement;
    const passwordInput = document.querySelector('input[name="password"]') as HTMLInputElement;
    
    if (emailInput && passwordInput) {
      emailInput.value = email;
      passwordInput.value = password;
      
      // Trigger change events
      emailInput.dispatchEvent(new Event('input', { bubbles: true }));
      passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
  };

  return (
    <div className="mb-4">
      <Alert variant="info" className="mb-3">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <i className="fas fa-info-circle me-2"></i>
            <strong>Demo Mode:</strong> Use test credentials to explore the dashboard
          </div>
          <Button
            variant="outline-info"
            size="sm"
            onClick={() => setShowCredentials(!showCredentials)}
          >
            {showCredentials ? 'Hide' : 'Show'} Test Logins
          </Button>
        </div>
      </Alert>

      <Collapse in={showCredentials}>
        <div>
          {credentials.map((cred, index) => (
            <Alert key={index} variant={cred.color} className="mb-2">
              <div className="d-flex justify-content-between align-items-start">
                <div>
                  <h6 className="mb-1">
                    <i className={`fas ${cred.type === 'Candidate' ? 'fa-user' : cred.type === 'Recruiter' ? 'fa-building' : 'fa-cog'} me-2`}></i>
                    {cred.type} Account
                  </h6>
                  <p className="mb-2 small">{cred.description}</p>
                  <div className="small">
                    <strong>Email:</strong> {cred.email}<br />
                    <strong>Password:</strong> {cred.password}
                  </div>
                </div>
                <Button
                  variant={`outline-${cred.color}`}
                  size="sm"
                  onClick={() => handleQuickLogin(cred.email, cred.password)}
                >
                  Quick Fill
                </Button>
              </div>
            </Alert>
          ))}
          
          <Alert variant="secondary" className="mb-0">
            <small>
              <i className="fas fa-lightbulb me-2"></i>
              <strong>Tip:</strong> Click "Quick Fill" to automatically populate the login form, then click "Sign In"
            </small>
          </Alert>
        </div>
      </Collapse>
    </div>
  );
};

export default LoginInfo;
