import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, Button, Badge, Tab, Tabs, Table } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import mockDataService, { MockJob } from '../../services/mockData';

interface RecruiterStats {
  jobsPosted: number;
  activeJobs: number;
  totalApplications: number;
  candidatesInterviewed: number;
  hires: number;
}

interface Candidate {
  id: string;
  name: string;
  title: string;
  location: string;
  skills: string[];
  experience: string;
  avatar?: string;
  appliedJobs: number;
  status: 'NEW' | 'REVIEWED' | 'SHORTLISTED' | 'INTERVIEWED' | 'HIRED' | 'REJECTED';
}

const RecruiterDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<RecruiterStats | null>(null);
  const [jobs, setJobs] = useState<MockJob[]>([]);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [statsData, jobsData, candidatesData] = await Promise.all([
        mockDataService.getRecruiterStats(),
        mockDataService.getJobs(),
        mockDataService.getCandidates()
      ]);
      
      setStats(statsData);
      setJobs(jobsData);
      setCandidates(candidatesData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCandidateStatusBadge = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'primary';
      case 'REVIEWED':
        return 'info';
      case 'SHORTLISTED':
        return 'warning';
      case 'INTERVIEWED':
        return 'success';
      case 'HIRED':
        return 'success';
      case 'REJECTED':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <div className="dashboard-container">
        <Container className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading dashboard...</span>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <Container>
        {/* Dashboard Header */}
        <div className="dashboard-header">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="dashboard-welcome">Welcome back, {user?.firstName}!</h1>
              <p className="dashboard-subtitle">
                Manage your job postings and review candidates
              </p>
            </div>
            <Button className="action-btn action-btn-primary">
              <i className="fas fa-plus me-2"></i>
              Post New Job
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="dashboard-stats-grid">
            <div className="modern-stat-card">
              <div className="stat-icon primary">
                <i className="fas fa-briefcase"></i>
              </div>
              <div className="modern-stat-number text-primary">{stats.jobsPosted}</div>
              <div className="modern-stat-label">Jobs Posted</div>
            </div>
            
            <div className="modern-stat-card">
              <div className="stat-icon success">
                <i className="fas fa-check-circle"></i>
              </div>
              <div className="modern-stat-number text-success">{stats.activeJobs}</div>
              <div className="modern-stat-label">Active Jobs</div>
            </div>
            
            <div className="modern-stat-card">
              <div className="stat-icon warning">
                <i className="fas fa-users"></i>
              </div>
              <div className="modern-stat-number text-warning">{stats.totalApplications}</div>
              <div className="modern-stat-label">Total Applications</div>
            </div>
            
            <div className="modern-stat-card">
              <div className="stat-icon info">
                <i className="fas fa-user-check"></i>
              </div>
              <div className="modern-stat-number text-info">{stats.hires}</div>
              <div className="modern-stat-label">Successful Hires</div>
            </div>
          </div>
        )}

        {/* Main Content Tabs */}
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k || 'overview')}
          className="mb-4"
        >
          <Tab eventKey="overview" title="Overview">
            <Row>
              <Col lg={8}>
                {/* Recent Job Postings */}
                <div className="dashboard-card mb-4">
                  <div className="dashboard-card-header">
                    <div className="d-flex justify-content-between align-items-center">
                      <h5 className="dashboard-card-title">Recent Job Postings</h5>
                      <Button className="action-btn action-btn-secondary" size="sm">
                        View All
                      </Button>
                    </div>
                  </div>
                  <div className="dashboard-card-body">
                    {jobs.slice(0, 3).map((job) => (
                      <div key={job.id} className="d-flex justify-content-between align-items-center p-3 border rounded mb-3">
                        <div>
                          <h6 className="mb-1 fw-semibold">{job.title}</h6>
                          <p className="mb-1 text-muted small">
                            <i className="fas fa-map-marker-alt me-1"></i>
                            {job.location}
                            {job.isRemote && <span className="ms-2 badge bg-success">Remote</span>}
                          </p>
                          <small className="text-muted">Posted: {new Date(job.postedDate).toLocaleDateString()}</small>
                        </div>
                        <div className="text-end">
                          <div className="mb-2">
                            <Badge bg="info">12 Applications</Badge>
                          </div>
                          <div>
                            <Button variant="outline-primary" size="sm" className="me-2">
                              View
                            </Button>
                            <Button variant="outline-secondary" size="sm">
                              Edit
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Col>
              
              <Col lg={4}>
                {/* Recent Candidates */}
                <div className="dashboard-card">
                  <div className="dashboard-card-header">
                    <h5 className="dashboard-card-title">Recent Candidates</h5>
                  </div>
                  <div className="dashboard-card-body">
                    {candidates.slice(0, 3).map((candidate) => (
                      <div key={candidate.id} className="d-flex align-items-center p-3 border rounded mb-3">
                        <img 
                          src={candidate.avatar} 
                          alt={candidate.name}
                          className="rounded-circle me-3"
                          style={{ width: '50px', height: '50px' }}
                        />
                        <div className="flex-grow-1">
                          <h6 className="mb-1 fw-semibold">{candidate.name}</h6>
                          <p className="mb-1 text-muted small">{candidate.title}</p>
                          <Badge bg={getCandidateStatusBadge(candidate.status)} className="small">
                            {candidate.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                    <div className="text-center">
                      <Button className="action-btn action-btn-secondary">
                        View All Candidates
                      </Button>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </Tab>

          <Tab eventKey="jobs" title="Job Postings">
            <div className="dashboard-card">
              <div className="dashboard-card-header">
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="dashboard-card-title">All Job Postings</h5>
                  <Button className="action-btn action-btn-primary">
                    <i className="fas fa-plus me-2"></i>
                    Post New Job
                  </Button>
                </div>
              </div>
              <div className="dashboard-card-body">
                <div className="table-responsive">
                  <Table hover>
                    <thead>
                      <tr>
                        <th>Job Title</th>
                        <th>Location</th>
                        <th>Type</th>
                        <th>Applications</th>
                        <th>Posted Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {jobs.map((job) => (
                        <tr key={job.id}>
                          <td className="fw-semibold">{job.title}</td>
                          <td>
                            {job.location}
                            {job.isRemote && <span className="ms-2 badge bg-success">Remote</span>}
                          </td>
                          <td>
                            <Badge bg="secondary">{job.type.replace('_', ' ')}</Badge>
                          </td>
                          <td>
                            <Badge bg="info">12</Badge>
                          </td>
                          <td>{new Date(job.postedDate).toLocaleDateString()}</td>
                          <td>
                            <Badge bg="success">Active</Badge>
                          </td>
                          <td>
                            <div className="d-flex gap-2">
                              <Button variant="outline-primary" size="sm">
                                <i className="fas fa-eye"></i>
                              </Button>
                              <Button variant="outline-secondary" size="sm">
                                <i className="fas fa-edit"></i>
                              </Button>
                              <Button variant="outline-danger" size="sm">
                                <i className="fas fa-trash"></i>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              </div>
            </div>
          </Tab>

          <Tab eventKey="candidates" title="Candidates">
            <div className="dashboard-card">
              <div className="dashboard-card-header">
                <h5 className="dashboard-card-title">Candidate Pipeline</h5>
              </div>
              <div className="dashboard-card-body">
                <div className="table-responsive">
                  <Table hover>
                    <thead>
                      <tr>
                        <th>Candidate</th>
                        <th>Title</th>
                        <th>Location</th>
                        <th>Experience</th>
                        <th>Applied Jobs</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {candidates.map((candidate) => (
                        <tr key={candidate.id}>
                          <td>
                            <div className="d-flex align-items-center">
                              <img 
                                src={candidate.avatar} 
                                alt={candidate.name}
                                className="rounded-circle me-2"
                                style={{ width: '40px', height: '40px' }}
                              />
                              <span className="fw-semibold">{candidate.name}</span>
                            </div>
                          </td>
                          <td>{candidate.title}</td>
                          <td>{candidate.location}</td>
                          <td>{candidate.experience}</td>
                          <td>
                            <Badge bg="info">{candidate.appliedJobs}</Badge>
                          </td>
                          <td>
                            <Badge bg={getCandidateStatusBadge(candidate.status)}>
                              {candidate.status}
                            </Badge>
                          </td>
                          <td>
                            <div className="d-flex gap-2">
                              <Button variant="outline-primary" size="sm">
                                <i className="fas fa-eye"></i>
                              </Button>
                              <Button variant="outline-success" size="sm">
                                <i className="fas fa-calendar"></i>
                              </Button>
                              <Button variant="outline-secondary" size="sm">
                                <i className="fas fa-envelope"></i>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              </div>
            </div>
          </Tab>
        </Tabs>
      </Container>
    </div>
  );
};

export default RecruiterDashboard;
