import React, { useState, useEffect } from 'react';
import { Card, Button, Form, Row, Col, Badge, ProgressBar, Modal, Alert } from 'react-bootstrap';
import mockDataService, { MockProfile } from '../../services/mockData';

interface ProfileManagementProps {
  onProfileUpdate?: (profile: MockProfile) => void;
}

const ProfileManagement: React.FC<ProfileManagementProps> = ({ onProfileUpdate }) => {
  const [profile, setProfile] = useState<MockProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editForm, setEditForm] = useState<Partial<MockProfile>>({});
  const [newSkill, setNewSkill] = useState('');

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const profileData = await mockDataService.getProfile();
      setProfile(profileData);
      setEditForm(profileData);
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!editForm) return;
    
    try {
      const updatedProfile = await mockDataService.updateProfile(editForm);
      setProfile(updatedProfile);
      setShowModal(false);
      setEditing(false);
      onProfileUpdate?.(updatedProfile);
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const handleAddSkill = () => {
    if (newSkill.trim() && editForm.skills) {
      setEditForm({
        ...editForm,
        skills: [...editForm.skills, newSkill.trim()]
      });
      setNewSkill('');
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    if (editForm.skills) {
      setEditForm({
        ...editForm,
        skills: editForm.skills.filter(skill => skill !== skillToRemove)
      });
    }
  };

  if (loading) {
    return (
      <div className="dashboard-card">
        <div className="dashboard-card-body text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading profile...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="dashboard-card">
        <div className="dashboard-card-body text-center">
          <Alert variant="warning">Unable to load profile data</Alert>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="dashboard-card">
        <div className="dashboard-card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="dashboard-card-title">
              <i className="fas fa-user-circle me-2 text-primary"></i>
              Profile Management
            </h5>
            <Button 
              className="action-btn action-btn-primary"
              onClick={() => setShowModal(true)}
            >
              <i className="fas fa-edit me-2"></i>
              Edit Profile
            </Button>
          </div>
        </div>
        
        <div className="dashboard-card-body">
          {/* Profile Completion */}
          <div className="mb-4">
            <div className="d-flex justify-content-between mb-2">
              <span className="fw-semibold">Profile Completion</span>
              <span className="fw-bold text-primary">{profile.profileCompletion}%</span>
            </div>
            <ProgressBar 
              now={profile.profileCompletion} 
              variant={profile.profileCompletion > 80 ? 'success' : 'warning'}
              style={{ height: '10px' }}
            />
            {profile.profileCompletion < 100 && (
              <small className="text-muted mt-1 d-block">
                Complete your profile to increase visibility to recruiters
              </small>
            )}
          </div>

          {/* Basic Info */}
          <Row className="mb-4">
            <Col md={3}>
              <div className="text-center">
                <img 
                  src={profile.avatar || 'https://via.placeholder.com/120x120/2563eb/ffffff?text=Profile'} 
                  alt="Profile" 
                  className="rounded-circle mb-2"
                  style={{ width: '120px', height: '120px', objectFit: 'cover' }}
                />
                <Button variant="outline-secondary" size="sm">
                  <i className="fas fa-camera me-1"></i>
                  Change Photo
                </Button>
              </div>
            </Col>
            <Col md={9}>
              <h4 className="mb-1">{profile.firstName} {profile.lastName}</h4>
              <p className="text-primary mb-2">{profile.title || 'Professional Title'}</p>
              <p className="text-muted mb-3">{profile.bio || 'No bio available'}</p>
              
              <div className="row">
                <div className="col-md-6">
                  <p className="mb-1">
                    <i className="fas fa-envelope me-2 text-muted"></i>
                    {profile.email}
                  </p>
                  {profile.phone && (
                    <p className="mb-1">
                      <i className="fas fa-phone me-2 text-muted"></i>
                      {profile.phone}
                    </p>
                  )}
                </div>
                <div className="col-md-6">
                  {profile.location && (
                    <p className="mb-1">
                      <i className="fas fa-map-marker-alt me-2 text-muted"></i>
                      {profile.location}
                    </p>
                  )}
                </div>
              </div>
            </Col>
          </Row>

          {/* Skills */}
          <div className="mb-4">
            <h6 className="fw-semibold mb-3">Skills</h6>
            <div className="d-flex flex-wrap gap-2">
              {profile.skills.map((skill, index) => (
                <Badge key={index} bg="primary" className="px-3 py-2">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>

          {/* Experience */}
          <div className="mb-4">
            <h6 className="fw-semibold mb-3">Experience</h6>
            {profile.experience.map((exp, index) => (
              <div key={index} className="border-start border-primary ps-3 mb-3">
                <h6 className="mb-1">{exp.position}</h6>
                <p className="text-primary mb-1">{exp.company}</p>
                <p className="text-muted small mb-2">
                  {exp.startDate} - {exp.endDate || 'Present'}
                </p>
                <p className="mb-0">{exp.description}</p>
              </div>
            ))}
          </div>

          {/* Education */}
          <div>
            <h6 className="fw-semibold mb-3">Education</h6>
            {profile.education.map((edu, index) => (
              <div key={index} className="border-start border-success ps-3 mb-3">
                <h6 className="mb-1">{edu.degree} in {edu.field}</h6>
                <p className="text-success mb-1">{edu.institution}</p>
                <p className="text-muted small mb-0">
                  {edu.startDate} - {edu.endDate || 'Present'}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Edit Profile Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Profile</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name</Form.Label>
                  <Form.Control
                    type="text"
                    value={editForm.firstName || ''}
                    onChange={(e) => setEditForm({...editForm, firstName: e.target.value})}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Last Name</Form.Label>
                  <Form.Control
                    type="text"
                    value={editForm.lastName || ''}
                    onChange={(e) => setEditForm({...editForm, lastName: e.target.value})}
                  />
                </Form.Group>
              </Col>
            </Row>
            
            <Form.Group className="mb-3">
              <Form.Label>Professional Title</Form.Label>
              <Form.Control
                type="text"
                value={editForm.title || ''}
                onChange={(e) => setEditForm({...editForm, title: e.target.value})}
                placeholder="e.g., Senior Full Stack Developer"
              />
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Bio</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={editForm.bio || ''}
                onChange={(e) => setEditForm({...editForm, bio: e.target.value})}
                placeholder="Tell us about yourself..."
              />
            </Form.Group>
            
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="tel"
                    value={editForm.phone || ''}
                    onChange={(e) => setEditForm({...editForm, phone: e.target.value})}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Location</Form.Label>
                  <Form.Control
                    type="text"
                    value={editForm.location || ''}
                    onChange={(e) => setEditForm({...editForm, location: e.target.value})}
                    placeholder="City, Country"
                  />
                </Form.Group>
              </Col>
            </Row>
            
            {/* Skills Management */}
            <Form.Group className="mb-3">
              <Form.Label>Skills</Form.Label>
              <div className="d-flex mb-2">
                <Form.Control
                  type="text"
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  placeholder="Add a skill"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddSkill())}
                />
                <Button 
                  variant="outline-primary" 
                  className="ms-2"
                  onClick={handleAddSkill}
                >
                  Add
                </Button>
              </div>
              <div className="d-flex flex-wrap gap-2">
                {editForm.skills?.map((skill, index) => (
                  <Badge 
                    key={index} 
                    bg="primary" 
                    className="px-3 py-2 d-flex align-items-center"
                  >
                    {skill}
                    <button
                      type="button"
                      className="btn-close btn-close-white ms-2"
                      style={{ fontSize: '0.7rem' }}
                      onClick={() => handleRemoveSkill(skill)}
                    ></button>
                  </Badge>
                ))}
              </div>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSaveProfile}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ProfileManagement;
