import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, <PERSON>, Col, <PERSON>, Button, Badge, Alert, Tab, Tabs } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import mockDataService, { MockStats, MockApplication, MockJob } from '../../services/mockData';
import ProfileManagement from './ProfileManagement';
import DocumentManagement from './DocumentManagement';

const CandidateDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<MockStats | null>(null);
  const [recentApplications, setRecentApplications] = useState<MockApplication[]>([]);
  const [recommendedJobs, setRecommendedJobs] = useState<MockJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [statsData, applicationsData, jobsData] = await Promise.all([
        mockDataService.getStats(),
        mockDataService.getApplications(),
        mockDataService.getJobs()
      ]);
      
      setStats(statsData);
      setRecentApplications(applicationsData);
      setRecommendedJobs(jobsData.slice(0, 3));
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'INTERVIEW_SCHEDULED':
      case 'INTERVIEWED':
        return 'success';
      case 'REVIEWED':
        return 'info';
      case 'OFFERED':
        return 'warning';
      case 'ACCEPTED':
        return 'success';
      case 'REJECTED':
        return 'danger';
      case 'WITHDRAWN':
        return 'secondary';
      default:
        return 'primary';
    }
  };

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="dashboard-container">
        <Container className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading dashboard...</span>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <Container>
        {/* Dashboard Header */}
        <div className="dashboard-header">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="dashboard-welcome">Welcome back, {user?.firstName}!</h1>
              <p className="dashboard-subtitle">
                Track your applications and discover new opportunities
              </p>
            </div>
            <Button className="action-btn action-btn-primary">
              <i className="fas fa-search me-2"></i>
              Browse Jobs
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="dashboard-stats-grid">
            <div className="modern-stat-card">
              <div className="stat-icon primary">
                <i className="fas fa-paper-plane"></i>
              </div>
              <div className="modern-stat-number text-primary">{stats.applications.total}</div>
              <div className="modern-stat-label">Applications Sent</div>
            </div>
            
            <div className="modern-stat-card">
              <div className="stat-icon success">
                <i className="fas fa-calendar-check"></i>
              </div>
              <div className="modern-stat-number text-success">{stats.applications.interviews}</div>
              <div className="modern-stat-label">Interviews Scheduled</div>
            </div>
            
            <div className="modern-stat-card">
              <div className="stat-icon warning">
                <i className="fas fa-eye"></i>
              </div>
              <div className="modern-stat-number text-warning">{stats.profile.views}</div>
              <div className="modern-stat-label">Profile Views</div>
            </div>
            
            <div className="modern-stat-card">
              <div className="stat-icon info">
                <i className="fas fa-bookmark"></i>
              </div>
              <div className="modern-stat-number text-info">{stats.jobs.saved}</div>
              <div className="modern-stat-label">Saved Jobs</div>
            </div>
          </div>
        )}

        {/* Main Content Tabs */}
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k || 'overview')}
          className="mb-4"
        >
          <Tab eventKey="overview" title="Overview">
            <Row>
              <Col lg={8}>
                {/* Recent Applications */}
                <div className="dashboard-card mb-4">
                  <div className="dashboard-card-header">
                    <h5 className="dashboard-card-title">Recent Applications</h5>
                  </div>
                  <div className="dashboard-card-body">
                    {recentApplications.length > 0 ? (
                      <div>
                        {recentApplications.map((application) => (
                          <div key={application.id} className="d-flex justify-content-between align-items-center p-3 border rounded mb-3">
                            <div>
                              <h6 className="mb-1 fw-semibold">{application.jobTitle}</h6>
                              <p className="mb-1 text-muted small">{application.company}</p>
                              <small className="text-muted">Applied: {new Date(application.appliedDate).toLocaleDateString()}</small>
                            </div>
                            <div className="text-end">
                              <Badge bg={getStatusBadgeVariant(application.status)} className="mb-2">
                                {formatStatus(application.status)}
                              </Badge>
                              {application.interviewDate && (
                                <div>
                                  <small className="text-success">
                                    <i className="fas fa-calendar me-1"></i>
                                    Interview: {new Date(application.interviewDate).toLocaleDateString()}
                                  </small>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                        <div className="text-center">
                          <Button className="action-btn action-btn-secondary">
                            View All Applications
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <i className="fas fa-briefcase fa-3x text-muted mb-3"></i>
                        <p className="text-muted">No applications yet. Start applying to jobs!</p>
                        <Button className="action-btn action-btn-primary">
                          Browse Jobs
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </Col>
              
              <Col lg={4}>
                {/* Recommended Jobs */}
                <div className="dashboard-card">
                  <div className="dashboard-card-header">
                    <h5 className="dashboard-card-title">Recommended Jobs</h5>
                  </div>
                  <div className="dashboard-card-body">
                    {recommendedJobs.map((job) => (
                      <div key={job.id} className="border rounded p-3 mb-3">
                        <h6 className="mb-2 fw-semibold">{job.title}</h6>
                        <p className="mb-1 text-muted small">{job.company}</p>
                        <p className="mb-2 text-muted small">
                          <i className="fas fa-map-marker-alt me-1"></i>
                          {job.location}
                          {job.isRemote && <span className="ms-2 badge bg-success">Remote</span>}
                        </p>
                        <div className="d-flex justify-content-between align-items-center">
                          <small className="text-success fw-semibold">
                            ${job.salary.min.toLocaleString()} - ${job.salary.max.toLocaleString()}
                          </small>
                          <Button size="sm" className="action-btn action-btn-primary">
                            Apply
                          </Button>
                        </div>
                      </div>
                    ))}
                    <div className="text-center">
                      <Button className="action-btn action-btn-secondary">
                        View More Jobs
                      </Button>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </Tab>

          <Tab eventKey="profile" title="Profile">
            <ProfileManagement />
          </Tab>

          <Tab eventKey="documents" title="Documents">
            <DocumentManagement />
          </Tab>

          <Tab eventKey="applications" title="Applications">
            <div className="dashboard-card">
              <div className="dashboard-card-header">
                <h5 className="dashboard-card-title">All Applications</h5>
              </div>
              <div className="dashboard-card-body">
                {recentApplications.length > 0 ? (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Job Title</th>
                          <th>Company</th>
                          <th>Applied Date</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentApplications.map((application) => (
                          <tr key={application.id}>
                            <td className="fw-semibold">{application.jobTitle}</td>
                            <td>{application.company}</td>
                            <td>{new Date(application.appliedDate).toLocaleDateString()}</td>
                            <td>
                              <Badge bg={getStatusBadgeVariant(application.status)}>
                                {formatStatus(application.status)}
                              </Badge>
                            </td>
                            <td>
                              <Button variant="outline-primary" size="sm" className="me-2">
                                View
                              </Button>
                              <Button variant="outline-secondary" size="sm">
                                Withdraw
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-5">
                    <i className="fas fa-briefcase fa-3x text-muted mb-3"></i>
                    <h6 className="text-muted">No applications found</h6>
                    <p className="text-muted">Start applying to jobs to see them here</p>
                    <Button className="action-btn action-btn-primary">
                      Browse Jobs
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Tab>
        </Tabs>
      </Container>
    </div>
  );
};

export default CandidateDashboard;
