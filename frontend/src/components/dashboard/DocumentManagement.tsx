import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Table, Badge, Modal, Form, Alert, ProgressBar } from 'react-bootstrap';
import mockDataService, { MockDocument } from '../../services/mockData';

interface DocumentManagementProps {
  onDocumentUpdate?: (documents: MockDocument[]) => void;
}

const DocumentManagement: React.FC<DocumentManagementProps> = ({ onDocumentUpdate }) => {
  const [documents, setDocuments] = useState<MockDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState<MockDocument['type']>('RESUME');
  const [uploadError, setUploadError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const documentsData = await mockDataService.getDocuments();
      setDocuments(documentsData);
      onDocumentUpdate?.(documentsData);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDocumentIcon = (type: MockDocument['type']) => {
    switch (type) {
      case 'RESUME':
        return 'fas fa-file-alt text-primary';
      case 'COVER_LETTER':
        return 'fas fa-file-text text-info';
      case 'PORTFOLIO':
        return 'fas fa-folder text-warning';
      case 'CERTIFICATE':
        return 'fas fa-certificate text-success';
      default:
        return 'fas fa-file text-muted';
    }
  };

  const getDocumentTypeBadge = (type: MockDocument['type']) => {
    const variants = {
      RESUME: 'primary',
      COVER_LETTER: 'info',
      PORTFOLIO: 'warning',
      CERTIFICATE: 'success',
      OTHER: 'secondary'
    };
    return variants[type] || 'secondary';
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setUploadError('File size must be less than 10MB');
        return;
      }
      
      // Validate file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        setUploadError('Only PDF and Word documents are allowed');
        return;
      }
      
      setSelectedFile(file);
      setUploadError('');
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;
    
    try {
      setUploading(true);
      setUploadProgress(0);
      
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);
      
      const newDocument = await mockDataService.uploadDocument(selectedFile, documentType);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        setDocuments(prev => [newDocument, ...prev]);
        setShowUploadModal(false);
        setSelectedFile(null);
        setUploadProgress(0);
        onDocumentUpdate?.([newDocument, ...documents]);
      }, 500);
      
    } catch (error) {
      console.error('Error uploading document:', error);
      setUploadError('Failed to upload document. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = (documentId: string) => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      const updatedDocuments = documents.filter(doc => doc.id !== documentId);
      setDocuments(updatedDocuments);
      onDocumentUpdate?.(updatedDocuments);
    }
  };

  const toggleVisibility = (documentId: string) => {
    const updatedDocuments = documents.map(doc => 
      doc.id === documentId ? { ...doc, isPublic: !doc.isPublic } : doc
    );
    setDocuments(updatedDocuments);
    onDocumentUpdate?.(updatedDocuments);
  };

  if (loading) {
    return (
      <div className="dashboard-card">
        <div className="dashboard-card-body text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading documents...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="dashboard-card">
        <div className="dashboard-card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="dashboard-card-title">
              <i className="fas fa-file-alt me-2 text-primary"></i>
              Document Management
            </h5>
            <Button 
              className="action-btn action-btn-primary"
              onClick={() => setShowUploadModal(true)}
            >
              <i className="fas fa-upload me-2"></i>
              Upload Document
            </Button>
          </div>
        </div>
        
        <div className="dashboard-card-body">
          {documents.length > 0 ? (
            <div className="table-responsive">
              <Table hover>
                <thead>
                  <tr>
                    <th>Document</th>
                    <th>Type</th>
                    <th>Size</th>
                    <th>Uploaded</th>
                    <th>Visibility</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {documents.map((document) => (
                    <tr key={document.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <i className={`${getDocumentIcon(document.type)} me-2`}></i>
                          <span className="fw-semibold">{document.name}</span>
                        </div>
                      </td>
                      <td>
                        <Badge bg={getDocumentTypeBadge(document.type)}>
                          {document.type.replace('_', ' ')}
                        </Badge>
                      </td>
                      <td className="text-muted">{formatFileSize(document.size)}</td>
                      <td className="text-muted">
                        {new Date(document.uploadDate).toLocaleDateString()}
                      </td>
                      <td>
                        <Button
                          variant={document.isPublic ? 'success' : 'outline-secondary'}
                          size="sm"
                          onClick={() => toggleVisibility(document.id)}
                        >
                          <i className={`fas ${document.isPublic ? 'fa-eye' : 'fa-eye-slash'} me-1`}></i>
                          {document.isPublic ? 'Public' : 'Private'}
                        </Button>
                      </td>
                      <td>
                        <div className="d-flex gap-2">
                          <Button variant="outline-primary" size="sm">
                            <i className="fas fa-download"></i>
                          </Button>
                          <Button variant="outline-info" size="sm">
                            <i className="fas fa-eye"></i>
                          </Button>
                          <Button 
                            variant="outline-danger" 
                            size="sm"
                            onClick={() => handleDelete(document.id)}
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-5">
              <i className="fas fa-file-upload fa-3x text-muted mb-3"></i>
              <h6 className="text-muted">No documents uploaded yet</h6>
              <p className="text-muted mb-3">Upload your resume, portfolio, and certificates to get started</p>
              <Button 
                className="action-btn action-btn-primary"
                onClick={() => setShowUploadModal(true)}
              >
                <i className="fas fa-upload me-2"></i>
                Upload Your First Document
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Upload Modal */}
      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Upload Document</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {uploadError && (
            <Alert variant="danger" className="mb-3">
              {uploadError}
            </Alert>
          )}
          
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Document Type</Form.Label>
              <Form.Select 
                value={documentType} 
                onChange={(e) => setDocumentType(e.target.value as MockDocument['type'])}
              >
                <option value="RESUME">Resume</option>
                <option value="COVER_LETTER">Cover Letter</option>
                <option value="PORTFOLIO">Portfolio</option>
                <option value="CERTIFICATE">Certificate</option>
                <option value="OTHER">Other</option>
              </Form.Select>
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Select File</Form.Label>
              <Form.Control
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept=".pdf,.doc,.docx"
              />
              <Form.Text className="text-muted">
                Supported formats: PDF, DOC, DOCX (Max size: 10MB)
              </Form.Text>
            </Form.Group>
            
            {selectedFile && (
              <Alert variant="info">
                <strong>Selected:</strong> {selectedFile.name} ({formatFileSize(selectedFile.size)})
              </Alert>
            )}
            
            {uploading && (
              <div className="mb-3">
                <div className="d-flex justify-content-between mb-1">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <ProgressBar now={uploadProgress} />
              </div>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button 
            variant="secondary" 
            onClick={() => setShowUploadModal(false)}
            disabled={uploading}
          >
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
          >
            {uploading ? 'Uploading...' : 'Upload Document'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default DocumentManagement;
