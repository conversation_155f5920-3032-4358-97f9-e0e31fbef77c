import React from 'react';
import { Container, <PERSON>, Col } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';

const Footer: React.FC = () => {
  return (
    <footer className="footer-dark py-5 mt-auto">
      <Container>
        <Row>
          <Col md={4} className="mb-4">
            <h5 className="text-white mb-3">BytePath</h5>
            <p className="text-light mb-3">
              Connecting Africa's top tech talent with innovative companies worldwide.
            </p>
            <div className="d-flex gap-3">
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-linkedin"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-github"></i>
              </a>
            </div>
          </Col>
          
          <Col md={2} className="mb-4">
            <h6 className="text-white mb-3">For Talent</h6>
            <ul className="list-unstyled">
              <li className="mb-2">
                <Link to="/jobs" className="text-gray-400 text-decoration-none hover:text-white">
                  Browse Jobs
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/companies" className="text-gray-400 text-decoration-none hover:text-white">
                  Companies
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/register" className="text-gray-400 text-decoration-none hover:text-white">
                  Create Profile
                </Link>
              </li>
            </ul>
          </Col>
          
          <Col md={2} className="mb-4">
            <h6 className="text-white mb-3">For Employers</h6>
            <ul className="list-unstyled">
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Post Jobs
                </a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Find Talent
                </a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Pricing
                </a>
              </li>
            </ul>
          </Col>
          
          <Col md={2} className="mb-4">
            <h6 className="text-white mb-3">Resources</h6>
            <ul className="list-unstyled">
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Blog
                </a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Help Center
                </a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Contact
                </a>
              </li>
            </ul>
          </Col>
          
          <Col md={2} className="mb-4">
            <h6 className="text-white mb-3">Legal</h6>
            <ul className="list-unstyled">
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Privacy Policy
                </a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-gray-400 text-decoration-none hover:text-white">
                  Terms of Service
                </a>
              </li>
            </ul>
          </Col>
        </Row>
        
        <hr className="my-4 border-gray-700" />
        
        <Row className="align-items-center">
          <Col md={6}>
            <p className="text-gray-400 mb-0">
              © 2024 BytePath. All rights reserved.
            </p>
          </Col>
          <Col md={6} className="text-md-end">
            <p className="text-gray-400 mb-0">
              Made with ❤️ for Africa's tech community
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
